﻿##theme configurations
$folderName = /dir-start
$siteTitle = Business Directory
$customPostKey = listing
$customTaxonomyKey = directory
$themeName = Directory
$directoryGoogleCat =
$directoryCountry = US
$colorPalettes = []




$customFields = [title        description        category        feature_id        address        address_info_borough        address_info_address        address_info_city        address_info_zip        address_info_region        address_info_country_code        place_id        phone        url        logo        main_image        latitude        longitude        attributes        place_topics        rating        hotel_rating        price_level        people_also_search        work_time        popular_times        contact_info        check_url]




Please refer to the theme configuration variables set above.
Can you create a custom theme in folder $folderName that will be used for a directory wordpress website called $themeName.
The site is about $siteTitle.
Create only these basic files to start the theme
-functions.php
-index.php
-style.css
-page.php
In the functions, add a custom post type key "$customPostKey" that has a custom taxonomy key "$customTaxonomyKey" which is hierarchical.
Make the page.php responsive with modern web design and responsive.




Create all these 3 templates below with all the details as mentioned.
Make them look attractive, professional, modern web design and responsive.
Use proper contrast.
Apply icons when possible.
Apply these color palettes $colorPalettes to the theme.


Template 1. (Filename: taxonomy-$customTaxonomyKey.php)
- if it's the First Level Taxonomy then do the following:
        - H1 tag will be - "$directoryGoogleCat in [First Level Taxonomy name]"
        - List all the children of each taxonomy with permalinks and how many custom post types within each child.
        - Display in cards layout (4 each row).
        - Add a navigation link back to the homepage.


if it's a Child Taxonomy (with parent taxonomy) then do the following:
        - list all the custom post types in this child taxonomy.
        - H1 tag will be - "$directoryGoogleCat near [Child Taxonomy Term Name], [Parent Taxonomy Term Name]"
        - Add a navigation link back to its Parent Taxonomy.
        - Use card layout to display the posts with 3 cards each row.
        - Make the cards with permalinks of the posts.
        - Display the following details in each card/post with permalink.


            -Show the following WordPress Core Fields:
            --Title
            --Featured image (add a logic that if the featured image is broken, render the image using this src: https://placehold.co/600x400?text=$siteTitle)


            -Show these fields from ACF:
            --- work_time - this is in json format, sample format is
                {"work_hours":{"timetable":{"sunday":[{"open":{"hour":0,"minute":0},"close":{"hour":0,"minute":0}}],"monday":[{"open":{"hour":0,"minute":0},"close":{"hour":0,"minute":0}}],"tuesday":[{"open":{"hour":0,"minute":0},"close":{"hour":0,"minute":0}}],"wednesday":[{"open":{"hour":0,"minute":0},"close":{"hour":0,"minute":0}}],"thursday":[{"open":{"hour":0,"minute":0},"close":{"hour":0,"minute":0}}],"friday":[{"open":{"hour":0,"minute":0},"close":{"hour":0,"minute":0}}],"saturday":[{"open":{"hour":0,"minute":0},"close":{"hour":0,"minute":0}}]},"current_status":"open"}}
                Display this in a nice human readable format.


            --- address
--- url - this is the website
            --- rating - this is in json format, sample format is
                {"rating_type":"Max5","value":5,"votes_count":1,"rating_max":null}
                Display this in a nice human readable format.
–- place_topics - Label it as “Review Topics” - This is in json format, the format is:
    {"water":15,"fenced":16,"wind":11,"agility":4,"interaction":2,"perimeter":2,"volunteers":2,"picnic table":3,"training":2,"furry":2}
    
Render the json value in a nice human readable format.




Template 2. For custom post type (Filename: single-$customPostKey.php)
Display the following:


- the H1 title should be [Post Title] in [address_info_city(acf field)], [address_info_region(acf field)]
- Add a navigation link back to the taxonomy where it belongs.
- featured image (add a logic that if the featured image is broken, render the image using this src: https://placehold.co/600x400?text=$siteTitle)
- content - do not use the typical the_content(),
use $raw_content = get_the_content() - this is in json content with the following format:
{"available_attributes":{"service_options":["has_onsite_services"],"accessibility":["has_wheelchair_accessible_parking","has_wheelchair_accessible_entrance"]},"unavailable_attributes":null}


Can you parse the json format to display the available_attributes and unavailable_attributes? For example in the given format:
service options will contain has_onsite_services
and
accessibility will contain has_wheelchair_accessible_parking, has_wheelchair_accessible_entrance
Handle the displaying of the value in human readable format. For e.g has_wheelchair_accessible_entrance it should be Has wheelchair accessible entrance or whichever way you think it will display nicely.


- work_time(acf field) - this is in json format, sample format is:


{"work_hours":{"timetable":{"sunday":[{"open":{"hour":0,"minute":0},"close":{"hour":0,"minute":0}}],"monday":[{"open":{"hour":0,"minute":0},"close":{"hour":0,"minute":0}}],"tuesday":[{"open":{"hour":0,"minute":0},"close":{"hour":0,"minute":0}}],"wednesday":[{"open":{"hour":0,"minute":0},"close":{"hour":0,"minute":0}}],"thursday":[{"open":{"hour":0,"minute":0},"close":{"hour":0,"minute":0}}],"friday":[{"open":{"hour":0,"minute":0},"close":{"hour":0,"minute":0}}],"saturday":[{"open":{"hour":0,"minute":0},"close":{"hour":0,"minute":0}}]},"current_status":"open"}}


Render the json value in a nice human readable format.


- address(acf field)
- contact_info(acf field) - this is in json format, sample format is:


[{"type":"telephone","value":"+***********","source":"google_business"}]


Render the json value in a nice human readable format.


- check_url(acf field) - this is a map url
- url(acf field) - this is the website of the listing
- description (acf field)
- rating (acf field) - this is in json format, sample format is
                {"rating_type":"Max5","value":5,"votes_count":1,"rating_max":null}
                Display this in a nice human readable format.
- place_topics (acf field) - Label it as “Review Topics” - This is in json format, the format is:
    {"water":15,"fenced":16,"wind":11,"agility":4,"interaction":2,"perimeter":2,"volunteers":2,"picnic table":3,"training":2,"furry":2}
    
Render the json in a nice human readable format.




- Add another section in single-$customPostKey.php with H2 tag Title as "Nearby $directoryGoogleCat in [address_info_city(acf field)], [address_info_region(acf field)]".
Display all the posts in the same child taxonomy in cards layout (2 each row).
Show the following in card (with permalink):
-- Title (in h3 tag)
-- Featured image (add a logic that if the featured image is broken, render the image using this src: https://placehold.co/600x400?text=[Title of the page])




Template 3. Template for Homepage. (Filename: front-page.php)
        - Add a Hero section with the title of the site and 1 sentence description.
        - Use assets/hero-section.png as the path for the hero section image background. Do not create the png, it will be added manually. Make sure proper contrast is applied.


        - Add another section with the title in h3 "Search $directoryGoogleCat by City".
        - Show 2 dropdowns.
        - the 1st dropdown should contain all the First Level Taxonomy.
        - the 2nd dropdown should load the children taxonomy based on the selected First Level Taxonomy in the 1st Dropdown, create a jquery js to achieve this.
        - When a value of is selected in the second dropdown, redirect the page to that child taxonomy permalink, create a jquery js to achieve this.
        - You may refer to this sample JS to achieve it:


        jQuery(document).ready(function($) {
            $('#first-level-taxonomy').on('change', function() {
                var parent_id = $(this).val();
                var $child_dropdown = $('#child-taxonomy');
                $child_dropdown.empty().append('<option value="">Select City</option>');


                if (parent_id) {
                    console.log('AJAX request initiated with parent_id:', parent_id);
                    $.ajax({
                        url: mosque_ajax_object.ajax_url, // WordPress AJAX URL
                        type: 'POST',
                        data: {
                            action: 'get_child_taxonomies',
                            parent_id: parent_id,
                            taxonomy: 'state' // Your taxonomy slug
                        },
                        success: function(response) {
                            console.log('AJAX success response:', response);
                            if (response && response.length > 0) {
                                $.each(response, function(index, term) {
                                    $child_dropdown.append('<option value="' + term.link + '">' + term.name + '</option>');
                                });
                                $child_dropdown.prop('disabled', false);
                            } else {
                                $child_dropdown.prop('disabled', true);
                                console.log('No child taxonomies found or empty response.');
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error('AJAX error:', status, error);
                            $child_dropdown.prop('disabled', true);
                        }
                    });
                } else {
                    $child_dropdown.prop('disabled', true);
                    console.log('Parent ID is empty, disabling child dropdown.');
                }
            });


            $('#child-taxonomy').on('change', function() {
                var term_link = $(this).val();
                if (term_link) {
                    window.location.href = term_link;
                }
            });
        });


        - Add another section with the title in h3 "Browse $directoryGoogleCat by State".
            - Display all Parents Taxonomy in cards layout (3 each row).
            - The title will be in h4 with permalink.
            - Display the description if available.
            - Render the taxonomy image if available.


        - Add another section with the title in h3 "Popular $directoryGoogleCat around $directoryCountry".
            - Display random custom posts type ($customPostKey). Display 25 in card layout.
            - Show the title, featured image (add a logic that if the featured image is broken, render the image using this src: https://placehold.co/600x400?text=$siteTitle), address(acf field), rating(acf field)


Create header.php and footer.php
Make the header styling look good and professional with modern web design, Use assets/logo.png as the logo path of the site and favicon, do not create the png it will be added manually.
Make sure the menu items added in the primary menu section will render correctly and will look good.
In the footer, Use assets/logo.png as the path to display the logo, do not create the png it will be added manually.
Make the footer styling look good and professional modern web design.
Make sure the menus added in the footer menu section will render correctly and will look well designed.


Create proper breadcrumbs in all of the 3 custom templates.


##CREATE ACF FIELDS
Create an ACF json sync file for these custom fields. All field types will be text only.
$customFields
These custom fields will be for the custom post type = $customPostKey